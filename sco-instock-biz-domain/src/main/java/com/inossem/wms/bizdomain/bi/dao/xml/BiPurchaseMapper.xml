<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.bi.dao.BiPurchaseMapper">

    <select id="selectContractAmountSum" resultType="java.math.BigDecimal">
        select
            ifnull((select SUM((h.demand_qty * IFNULL((select no_tax_price from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0))
                * (1 + IFNULL((select tax_code_rate from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0)))
             from biz_receipt_contract_head h
             where receipt_type = 403
               and receipt_status = 90
               and is_delete = 0
               and YEAR(create_time) = #{year}), 0)

                +

            ifnull((select sum(ifnull(i.tax_price * i.qty, 0))
             from biz_receipt_contract_head h
                      join biz_receipt_contract_item i on i.head_id = h.id and i.is_delete = 0
             where h.receipt_type = 402
               and h.receipt_status = 90
               and h.is_delete = 0
               and YEAR(h.create_time) = #{year}), 0)
    </select>

    <select id="selectContractDetailForAmountSum" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiContractAmountDetailVO">
        select
            h.id,
            h.currency,
            YEAR(h.create_time) as year,
            MONTH(h.create_time) as month,
            (h.demand_qty * IFNULL((select no_tax_price from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0)
                * (1 + IFNULL((select tax_code_rate from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0))) as amount
        from biz_receipt_contract_head h
        where receipt_type = 403
          and receipt_status = 90
          and is_delete = 0
          and YEAR(create_time) = #{year}

        UNION ALL

        select
            h.id,
            h.currency,
            YEAR(h.create_time) as year,
            MONTH(h.create_time) as month,
            ifnull(i.tax_price * i.qty, 0) as amount
        from biz_receipt_contract_head h
                 join biz_receipt_contract_item i on i.head_id = h.id and i.is_delete = 0
        where h.receipt_type = 402
          and h.receipt_status = 90
          and h.is_delete = 0
          and YEAR(h.create_time) = #{year}
    </select>

    <select id="getCompletedContractCount" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractCountVO">
        select year(create_time)                 year,
               month(create_time)                month,
               SUM(IF(receipt_type = 402, 1, 0)) count402,
               SUM(IF(receipt_type = 403, 1, 0)) count403,
               COUNT(1)                          count_total
        from biz_receipt_contract_head
        where receipt_type in (402, 403)
          and receipt_status = 90
          AND year(create_time) = #{po.year}
          <if test="po.month != null">
              AND month(create_time) &lt;= #{po.month}
          </if>
        group by year(create_time),
                 month(create_time)
    </select>

    <select id="getCompletedContractAmount402" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractAmountVO">
        select year(h.create_time)                 year,
               month(h.create_time)                month,
               sum(ifnull(i.tax_price * i.qty, 0)) amount402
        from biz_receipt_contract_head h
                 join biz_receipt_contract_item i on i.head_id = h.id and i.is_delete = 0
        where h.receipt_type = 403
          and h.receipt_status = 90
          and h.is_delete = 0
          AND year(h.create_time) = #{po.year}
          <if test="po.month != null">
              AND month(h.create_time) &lt;= #{po.month}
          </if>
        group by year(h.create_time),
                 month(h.create_time)
    </select>

    <select id="getCompletedContractAmount403" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractAmountVO">
        select year(create_time)                                                                                                                  year,
               month(create_time)                                                                                                                 month,
               SUM((h.demand_qty * IFNULL((select no_tax_price from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0))
                   * (1 + IFNULL((select tax_code_rate from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0))) amount403
        from biz_receipt_contract_head h
        where receipt_type = 403
          and receipt_status = 90
          and is_delete = 0
          AND year(create_time) = #{po.year}
          <if test="po.month != null">
              AND month(create_time) &lt;= #{po.month}
          </if>
        group by year(create_time),
                 month(create_time)
    </select>

    <select id="selectDemandPlanHead"
            resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchaseDemandPlanVO">
        select id,
               receipt_code,
               create_time,
               create_user_id,
               handle_user_id,
               modify_time,
               demand_plan_type,
               demand_type
        from biz_receipt_demand_plan_head
        where is_delete = 0
          and receipt_type = 400
    </select>

    <select id="selectPurchaseHead"
            resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchasePurchaseVO">
        select h.id,
               h.receipt_code,
               h.receipt_type,
               h.receipt_status,
               h.demand_plan_type,
               h.purchase_type,
               h.bid_method,
               h.purchase_subject,
               h.annual_budget_id,
               h.budget_amount,
               h.create_time,
               h.create_user_id,
               h.modify_time,
               ph.id demand_head_id
        from biz_receipt_purchase_apply_head h
                 left join biz_receipt_purchase_apply_item i on i.head_id = h.id and i.is_delete = 0
                 left join biz_receipt_demand_plan_head ph on ph.id = i.pre_receipt_id and ph.is_delete = 0
        where h.is_delete = 0
          and h.receipt_type in (401, 204, 4013, 4014)
        group by h.id
    </select>

    <select id="selectContractHead"
            resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchaseContractVO">
        select h.id,
               h.receipt_code,
               h.receipt_type,
               h.receipt_status,
               h.purchase_type,
               h.first_party,
               h.supplier_id,
               CASE WHEN h.receipt_type = 403 THEN i.no_tax_price ELSE 0 END                                            oil_price,
               CASE WHEN h.receipt_type = 403 THEN h.demand_qty * i.no_tax_price ELSE h.contract_amount_exclude_tax END amount,
               h.create_time,
               h.contract_sign_date,
               ah.id                                                                                                    purchase_head_id,
               ph.id                                                                                                    demand_head_id
        from biz_receipt_contract_head h
                 left join biz_receipt_contract_item i on i.head_id = h.id and i.is_delete = 0
                 left join biz_receipt_purchase_apply_head ah on (ah.id = i.pre_receipt_head_id or h.oil_purchase_code = ah.receipt_code) and ah.is_delete = 0
                 left join biz_receipt_purchase_apply_item ai on ai.head_id = ah.id and ai.is_delete = 0
                 left join biz_receipt_demand_plan_head ph on (ph.id = ai.pre_receipt_id or i.demand_plan_code = ph.receipt_code) and ph.is_delete = 0
        where h.is_delete = 0
          and h.receipt_type in (402, 403)
        group by h.id
    </select>

    <select id="selectBudgetClassifyList" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiBudgetClassifySubjectVO">
        SELECT dbc.id,
               dbc.budget_classify_code          as budget_code,
               dbc.budget_classify_name          as budget_name,
               sum(dab.budget_amount)            as budget_amount,
               sum(dab.existing_contract_amount) as contract_amount,
               su.user_code                      as create_user_code,
               su.user_name                      as create_user_name,
               dbc.create_time
        FROM dic_annual_budget dab
                 inner join dic_budget_classify dbc ON dbc.id = dab.budget_classify_id AND dbc.is_delete = 0
                 left join sys_user su ON dbc.create_user_id = su.id AND su.is_delete = 0
        where dab.is_delete = 0
          and dab.year = #{po.year}
        group by dbc.id
    </select>

    <select id="selectBudgetSubjectList" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiBudgetClassifySubjectVO">
        SELECT dbs.id,
               dbs.budget_subject_code           as budget_code,
               dbs.budget_subject_name           as budget_name,
               sum(dab.budget_amount)            as budget_amount,
               sum(dab.existing_contract_amount) as contract_amount,
               su.user_code                      as create_user_code,
               su.user_name                      as create_user_name,
               dbs.create_time
        FROM dic_annual_budget dab
                 inner join dic_budget_classify dbc ON dbc.id = dab.budget_classify_id AND dbc.is_delete = 0
                 inner join dic_budget_subject dbs ON dbs.id = dab.budget_subject_id AND dbs.is_delete = 0
                 left join sys_user su ON dbs.create_user_id = su.id AND su.is_delete = 0
        where dab.is_delete = 0
          and dab.year = #{po.year}
          and dab.budget_classify_id = #{po.budgetClassifyId}
        group by dbs.id
    </select>

    <select id="selectDemandNotPurchaseCount" resultType="java.lang.Long">
        select count(*)
        from bi_purchase_demand_plan d
                 left join bi_purchase_purchase p on p.demand_head_id = d.id
        where d.demand_plan_type != 30
          and p.id is null
    </select>

    <select id="selectDemandNotPurchaseCountUser" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiInPurchaseUserVO">
        select d.handle_user_id                             as user_id,
               '寻源竞价阶段'                               as stage_name,
               count(*)                                     as stage_count,
               -- 寻源阶段时间=采购申请的创建时间-需求计划审批完成时间
               ifnull((select avg(datediff(act.END_TIME_, dd.create_time))
                from bi_purchase_demand_plan dd
                         JOIN bi_purchase_purchase pp on pp.demand_head_id = dd.id AND pp.receipt_status = 90
                         JOIN (select ahp.BUSINESS_KEY_, aha.END_TIME_
                               from ACT_HI_PROCINST ahp
                                        LEFT JOIN ACT_HI_ACTINST aha ON ahp.ID_ = aha.PROC_INST_ID_ AND aha.ACT_ID_ = 'Level3ApprovalNode' AND aha.ASSIGNEE_ != ''
                               WHERE aha.END_TIME_ is not null
                               ORDER BY aha.END_TIME_ DESC) act ON act.BUSINESS_KEY_ = pp.receipt_code AND act.END_TIME_ is not null
                where dd.handle_user_id = d.handle_user_id
                  and year(dd.create_time) = #{po.year}), 0) as avg_days
        from bi_purchase_demand_plan d
                 left join bi_purchase_purchase p on p.demand_head_id = d.id
        where d.demand_plan_type != 30
          and p.id is null
          and d.handle_user_id != 0
          and year(d.create_time) = #{po.year}
        group by d.handle_user_id
    </select>

    <select id="selectPurchaseNotCompleteCount" resultType="java.lang.Long">
        select count(*)
        from bi_purchase_demand_plan d
                 inner join bi_purchase_purchase p on p.demand_head_id = d.id
        where p.receipt_status not in (90, 127)
    </select>

    <select id="selectPurchaseNotCompleteCountUser" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiInPurchaseUserVO">
        select d.handle_user_id                    as user_id,
               '采购申请阶段'                      as stage_name,
               count(*)                            as stage_count,
               -- 采购阶段的时间=合同创建时间-采购申请完成的时间
               ifnull((select avg(datediff(cc.create_time, pp.modify_time))
                from bi_purchase_demand_plan dd
                         JOIN bi_purchase_purchase pp on pp.demand_head_id = dd.id
                         JOIN bi_purchase_contract cc on cc.purchase_head_id = pp.id
                where dd.handle_user_id = d.handle_user_id
                  and year(dd.create_time) = #{po.year}), 0) as avg_days
        from bi_purchase_demand_plan d
                 inner join bi_purchase_purchase p on p.demand_head_id = d.id
        where p.receipt_status not in (90, 127)
          and d.handle_user_id != 0
          and year(d.create_time) = #{po.year}
        group by d.handle_user_id
    </select>

    <select id="selectPurchaseNotContractCount" resultType="java.lang.Long">
        select count(*)
        from bi_purchase_purchase p
                 left join bi_purchase_contract c on c.purchase_head_id = p.id
        where p.receipt_status = 90
          and c.id is null
    </select>

    <select id="selectPurchaseNotContractCountUser" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiInPurchaseUserVO">
        select d.handle_user_id                    as user_id,
               '定价审批中'                      as stage_name,
               count(*)                            as stage_count,
               -- 定价审批时间=合同的签订时间-采购申请的创建时间
               ifnull((select avg(datediff(cc.contract_sign_date, pp.create_time))
                from bi_purchase_demand_plan dd
                         JOIN bi_purchase_purchase pp on pp.demand_head_id = dd.id
                         JOIN bi_purchase_contract cc on cc.purchase_head_id = pp.id and cc.contract_sign_date is not null
                where dd.handle_user_id = d.handle_user_id
                  and year(dd.create_time) = #{po.year}), 0) as avg_days
        from bi_purchase_demand_plan d
                 join bi_purchase_purchase p on p.demand_head_id = d.id
                 left join bi_purchase_contract c on c.purchase_head_id = p.id
        where p.receipt_status = 90
          and c.id is null
          and d.handle_user_id != 0
          and year(d.create_time) = #{po.year}
        group by d.handle_user_id
    </select>

    <select id="selectContractCount" resultType="java.lang.Long">
        select count(*)
        from bi_purchase_contract c
        where c.receipt_status = 90
    </select>

    <select id="selectContractCountUser" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiInPurchaseUserVO">
        select d.handle_user_id                    as user_id,
               '合同签订'                        as stage_name,
               count(*)                            as stage_count,
               -- 合同签订时间=合同签订时间-需求创建时间
               ifnull((select avg(datediff(cc.contract_sign_date, dd.create_time))
                from bi_purchase_demand_plan dd
                         JOIN bi_purchase_contract cc on cc.demand_head_id = dd.id and cc.contract_sign_date is not null
                where dd.handle_user_id = d.handle_user_id
                  and year(dd.create_time) = #{po.year}), 0) as avg_days
        from bi_purchase_demand_plan d
                 join bi_purchase_contract c on c.demand_head_id = d.id
        where c.receipt_status = 90
          and d.handle_user_id != 0
          and year(d.create_time) = #{po.year}
        group by d.handle_user_id
    </select>

</mapper>
