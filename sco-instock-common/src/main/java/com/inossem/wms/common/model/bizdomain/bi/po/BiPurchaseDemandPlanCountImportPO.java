package com.inossem.wms.common.model.bizdomain.bi.po;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * BI采购当前需完成计划项目导入入参类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BiPurchaseDemandPlanCountImportPO", description = "BI采购当前需完成计划项目导入入参类")
public class BiPurchaseDemandPlanCountImportPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "年")
    @ExcelProperty(value = "年", index = 0)
    private Integer demandPlanYear;

    @ApiModelProperty(value = "月")
    @ExcelProperty(value = "月", index = 1)
    private Integer demandPlanMonth;

    @ApiModelProperty(value = "一般性需求计划")
    @ExcelProperty(value = "一般性需求计划", index = 2)
    private Long count10;

    @ApiModelProperty(value = "框架协议采购需求计划")
    @ExcelProperty(value = "框架协议采购需求计划", index = 3)
    private Long count20;

    @ApiModelProperty(value = "油品月度PO采购审批")
    @ExcelProperty(value = "油品月度PO采购审批", index = 4)
    private Long count40;

}
