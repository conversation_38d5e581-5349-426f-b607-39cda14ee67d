package com.inossem.wms.common.model.bizdomain.bi.vo;

import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "采购中数量查询传输对象", description = "采购中数量查询传输对象")
public class BiInPurchaseUserVO {

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "userCode,userName")
    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String userCode;

    @ApiModelProperty(value = "创建人名称" , example = "管理员")
    private String userName;

    @ApiModelProperty(value = "阶段分类")
    private String stageName;

    @ApiModelProperty(value = "单据数量")
    private Long stageCount;

    @ApiModelProperty(value = "阶段占比")
    private BigDecimal countRate;

    @ApiModelProperty(value = "平均时间")
    private BigDecimal avgDays;

}
