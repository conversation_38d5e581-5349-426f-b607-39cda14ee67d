package com.inossem.wms.common.model.bizdomain.bi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "采购中数量查询传输对象", description = "采购中数量查询传输对象")
public class BiInPurchaseVO {

    @ApiModelProperty(value = "总数")
    private Long totalCount;

    @ApiModelProperty(value = "已注册供应商数量")
    private List<BiInPurchaseSubVO> voList;

}
