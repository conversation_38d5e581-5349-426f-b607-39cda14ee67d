package com.inossem.wms.bizdomain.bi.service.biz;

import com.inossem.wms.bizdomain.bi.service.component.BiPurchaseComponent;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * BI-华信资源驾驶舱-采购类指标
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
@Slf4j
public class BiPurchaseService {

    @Autowired
    protected BiPurchaseComponent biPurchaseComponent;

    /**
     * BI-已完成采购任务的合同数量/金额
     */
    public void getCompletedContractSum(BizContext ctx) {
        biPurchaseComponent.getCompletedContractSum(ctx);
    }

    /**
     * BI-已完成采购任务的合同数量
     */
    public void getCompletedContractCount(BizContext ctx) {
        biPurchaseComponent.getCompletedContractCount(ctx);
    }

    /**
     * BI-已完成采购任务的合同金额
     */
    public void getCompletedContractAmount(BizContext ctx) {
        biPurchaseComponent.getCompletedContractAmount(ctx);
    }

    /**
     * BI-定时任务-保存采购类指标基础数据
     */
    public void saveBaseTable() {
        biPurchaseComponent.saveBaseTable();
    }

    /**
     * BI采购年度预算金额表-导入数据
     */
    public void importBudgetYearData(BizContext ctx) {
        biPurchaseComponent.importBudgetYearData(ctx);
    }

    /**
     * BI采购年度预算金额表-导出数据
     */
    public void exportBudgetYearExcel(BizContext ctx) {
        biPurchaseComponent.exportBudgetYearExcel(ctx);
    }

    /**
     * BI-采购类指标-预算达成率-总和
     */
    public void getBudgetSum(BizContext ctx) {
        biPurchaseComponent.getBudgetSum(ctx);
    }

    /**
     * BI-采购类指标-预算分类
     */
    public void getBudgetClassify(BizContext ctx) {
        biPurchaseComponent.getBudgetClassify(ctx);
    }

    /**
     * BI-采购类指标-预算科目
     */
    public void getBudgetSubject(BizContext ctx) {
        biPurchaseComponent.getBudgetSubject(ctx);
    }

    /**
     * BI-采购类指标-供应商总数量
     */
    public void getSupplierCount(BizContext ctx) {
        biPurchaseComponent.getSupplierCount(ctx);
    }

    /**
     * BI-采购类指标-采购中数量
     */
    public void getInPurchaseCount(BizContext ctx) {
        biPurchaseComponent.getInPurchaseCount(ctx);
    }

    /**
     * BI-采购类指标-采购中数量-用户列表
     */
    public void getInPurchaseCountUser(BizContext ctx) {
        biPurchaseComponent.getInPurchaseCountUser(ctx);
    }

    /**
     * BI采购当前需完成计划项目-导入数据
     */
    public void importDemandPlanCountData(BizContext ctx) {
        biPurchaseComponent.importDemandPlanCountData(ctx);
    }

    /**
     * BI采购当前需完成计划项目-导出数据
     */
    public void exportDemandPlanCountExcel(BizContext ctx) {
        biPurchaseComponent.exportDemandPlanCountExcel(ctx);
    }

}
