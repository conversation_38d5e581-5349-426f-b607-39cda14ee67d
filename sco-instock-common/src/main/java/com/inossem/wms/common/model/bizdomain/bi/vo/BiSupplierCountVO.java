package com.inossem.wms.common.model.bizdomain.bi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "供应商总数量查询传输对象", description = "供应商总数量查询传输对象")
public class BiSupplierCountVO {

    @ApiModelProperty(value = "已注册供应商数量")
    private Long registeredSupplierCount;

    @ApiModelProperty(value = "准入供应商数量")
    private Long admittedSupplierCount;

}
