package com.inossem.wms.common.model.bizdomain.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * BI采购合同表实体类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@TableName("bi_purchase_contract")
@ApiModel(value = "BI采购合同表底表", description = "BI采购合同表实体")
public class BiPurchaseContract implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "合同编号")
    private String receiptCode;

    @ApiModelProperty(value = "合同类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "甲方")
    private Integer firstParty;

    @ApiModelProperty(value = "供应商ID")
    private Long supplierId;

    @ApiModelProperty(value = "油品单价")
    private BigDecimal oilPrice;

    @ApiModelProperty(value = "合同总金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "合同创建时间")
    private Date createTime;

    @ApiModelProperty(value = "签订合同日期")
    private Date contractSignDate;

    @ApiModelProperty(value = "需求计划单据ID")
    private Long demandHeadId;

    @ApiModelProperty(value = "采购申请单据ID")
    private Long purchaseHeadId;

}
