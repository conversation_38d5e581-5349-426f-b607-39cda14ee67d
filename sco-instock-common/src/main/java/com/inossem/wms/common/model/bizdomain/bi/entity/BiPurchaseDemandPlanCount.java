package com.inossem.wms.common.model.bizdomain.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * BI采购当前需完成计划项目实体
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@TableName("bi_purchase_demand_plan_count")
@ApiModel(value = "BI采购当前需完成计划项目实体", description = "BI采购当前需完成计划项目实体")
public class BiPurchaseDemandPlanCount implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "年")
    private Integer demandPlanYear;

    @ApiModelProperty(value = "月")
    private Integer demandPlanMonth;

    @ApiModelProperty(value = "一般性需求计划")
    private Long count10;

    @ApiModelProperty(value = "框架协议采购需求计划")
    private Long count20;

    @ApiModelProperty(value = "油品月度PO采购审批")
    private Long count40;

}
