package com.inossem.wms.bizdomain.bi.service.datawrap;

import com.inossem.wms.bizdomain.bi.dao.BiPurchaseDemandPlanCountMapper;
import com.inossem.wms.common.model.bizdomain.bi.entity.BiPurchaseDemandPlanCount;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * BI采购当前需完成计划项目DataWrap服务类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
public class BiPurchaseDemandPlanCountDataWrap extends BaseDataWrap<BiPurchaseDemandPlanCountMapper, BiPurchaseDemandPlanCount> {

}
