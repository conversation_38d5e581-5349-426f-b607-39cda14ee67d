package com.inossem.wms.common.model.bizdomain.bi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "BI采购申请传输对象", description = "BI采购申请传输对象")
public class BiPurchasePurchaseVO {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "采购申请单号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "采购计划类型")
    private Integer demandPlanType;

    @ApiModelProperty(value = "采购类型")
    private Integer purchaseType;

    @ApiModelProperty(value = "采购方式")
    private String bidMethod;

    @ApiModelProperty(value = "采购主体")
    private String purchaseSubject;

    @ApiModelProperty(value = "预算出处")
    private Long annualBudgetId;

    @ApiModelProperty(value = "申请预算金额")
    private BigDecimal budgetAmount;

    @ApiModelProperty(value = "采购申请创建时间")
    private Date createTime;

    @ApiModelProperty(value = "采购申请创建人")
    private Long createUserId;

    @ApiModelProperty(value = "采购申请完成时间")
    private Date modifyTime;

    @ApiModelProperty(value = "需求计划单据ID")
    private Long demandHeadId;

}
